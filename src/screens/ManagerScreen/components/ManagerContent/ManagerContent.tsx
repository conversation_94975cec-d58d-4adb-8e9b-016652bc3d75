import './styles.scss';

import React from 'react';
import {useManager} from './../../useManager';
import {ManagerItem} from './../../interfaces';
import {
    DataExport,
    PendingSettlements,
    PendingSettlementsProvider,
    Stats,
    StatsProvider,
    Support,
    Verification,
} from './components';

export const ManagerContent = () => {
    const {activeMenu, availableTabs} = useManager();

    return (
        <div className='kas-manager-content'>
            {availableTabs.includes(ManagerItem.STATS) && (
                <div hidden={activeMenu !== ManagerItem.STATS}>
                    <StatsProvider>
                        <Stats />
                    </StatsProvider>
                </div>
            )}
            {availableTabs.includes(ManagerItem.DATA_EXPORT) && (
                <div hidden={activeMenu !== ManagerItem.DATA_EXPORT}>
                    <DataExport />
                </div>
            )}
            {availableTabs.includes(ManagerItem.PENDING_SETTLEMENTS) && (
                <div hidden={activeMenu !== ManagerItem.PENDING_SETTLEMENTS}>
                    <PendingSettlementsProvider>
                        <PendingSettlements />
                    </PendingSettlementsProvider>
                </div>
            )}
            {availableTabs.includes(ManagerItem.SUPPORT) && (
                <div hidden={activeMenu !== ManagerItem.SUPPORT}>
                    <Support />
                </div>
            )}
            {availableTabs.includes(ManagerItem.VERIFICATION) && (
                <div hidden={activeMenu !== ManagerItem.VERIFICATION}>
                    <Verification />
                </div>
            )}
        </div>
    );
};
