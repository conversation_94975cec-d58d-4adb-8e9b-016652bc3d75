import React, {useMemo} from 'react';
import {CreditReportCard, SkiptraceReportCard} from './../cards';
import {Grid2} from '@mui/material';
import {VerificationQueueDetailsModel} from '@/interfaces';
import {VerificationProfileSectionType} from '@/screens/VerificationScreen/components/VerificationContent/components/VerificationProfile/interfaces';
import {useVerificationProfile} from '@/screens/VerificationScreen/components/VerificationContent/components';
import {SkiptraceReportDetailsTabType} from '@/interfaces';

export const DepositContent = ({data}: {data: VerificationQueueDetailsModel}) => {
    const {activeSection} = useVerificationProfile();
    const skiptraceReportVisibleSections = [
        VerificationProfileSectionType.SSN,
        VerificationProfileSectionType.Phone_Number,
        VerificationProfileSectionType.Address,
    ];
    const creditReportVisibleSections = [
        VerificationProfileSectionType.SSN,
        VerificationProfileSectionType.Address,
    ];

    const defaultSkiptraceTab = useMemo(() => {
        switch (activeSection) {
            case VerificationProfileSectionType.SSN:
                return SkiptraceReportDetailsTabType.SSNs;
            case VerificationProfileSectionType.Phone_Number:
                return SkiptraceReportDetailsTabType.Phones;
            case VerificationProfileSectionType.Address:
                return SkiptraceReportDetailsTabType.Addresses;
            default:
                return undefined;
        }
    }, [activeSection]);

    return (
        <Grid2 container spacing={1}>
            <Grid2 size={12}>
                <div hidden={!activeSection || !skiptraceReportVisibleSections.includes(activeSection)}>
                    <SkiptraceReportCard data={data} defaultTab={defaultSkiptraceTab} />
                </div>
            </Grid2>
            <Grid2 size={12}>
                <div hidden={!activeSection || !creditReportVisibleSections.includes(activeSection)}>
                    <CreditReportCard data={data} />
                </div>
            </Grid2>
        </Grid2>
    );
};
