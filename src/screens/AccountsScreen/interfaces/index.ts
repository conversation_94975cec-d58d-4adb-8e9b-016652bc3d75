import {Dayjs} from 'dayjs';

export enum AccountItemType {
    File_Tracking = 'FILE TRACKING',
    File_Exchange = 'FILE EXCHANGE',
    Payments = 'PAYMENTS',
    Debit = 'DEBIT',
    Clearing = 'CLEARING',
    History = 'HISTORY',
    Banks = 'BANKS',
    Employer = 'EMPLOYER',
    Deductions = 'DEDUCTIONS',
}

export interface AccountsClearingParamsModel {
    endDate: Dayjs;
    assigned: boolean;
    cleared: boolean;
    future: boolean;
    employerId?: string;
}

export interface AccountsDebitsParamsModel {
    dateFrom: Dayjs;
    dateTo: Dayjs;
    assigned: boolean;
    billed: boolean;
    employerId?: string;
}

export interface AccountsPaymentsParamsModel {
    dateFrom: Dayjs;
    dateTo: Dayjs;
    assigned: boolean;
    billed: boolean;
    employerId?: string;
}

export interface AccountsFileExchangeParamsModel {
    assigned: boolean;
    employerId?: string;
}

export interface AccountsFileTrackingParamsModel {
    assigned: boolean;
    completed: boolean;
    length: number | null;
}

export interface AccountsDeductionsParamsModel {
    dateFrom: Dayjs;
    dateTo: Dayjs;
    employerId: string;
    payrollGroupId: string;
}
