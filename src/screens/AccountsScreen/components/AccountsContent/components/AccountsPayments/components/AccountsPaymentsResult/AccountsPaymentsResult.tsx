import React from 'react';
import {ColumnDef, Row} from '@tanstack/react-table';
import {AccountsPaymentsColumns} from './tables';
import {TableView} from '@/views';
import {useAccountsPayments} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsPaymentModel} from '@/interfaces';
import {PaymentDetailsExpand} from './components';

export const AccountsPaymentsResult = () => {
    const {paymentsState} = useAccountsPayments();

    return (
        <TableView<AccountsPaymentModel>
            withTableActions
            columns={AccountsPaymentsColumns as ColumnDef<AccountsPaymentModel, unknown>[]}
            loading={paymentsState.loading}
            error={paymentsState.error}
            data={paymentsState.data}
            tableName='Accounts Payments'
            renderExpand={(row: Row<AccountsPaymentModel>) => <PaymentDetailsExpand data={row.original} />}
            sortingColumns={[
                {id: 'payment_type', desc: false},
                {id: 'employer_name', desc: false},
            ]}
        />
    );
};
