import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    AccountsEmployerPayrollDetailsModel,
    AccountsEmployerSamplesModel,
    DataStateInterface,
} from '@/interfaces';

interface AccountsEmployerContextModel {
    payrollDetailsState: DataStateInterface<AccountsEmployerPayrollDetailsModel>;
    loadPayrollDetails: (id: string, searchParams: string) => Promise<void>;
    samplesState: DataStateInterface<AccountsEmployerSamplesModel[]>;
    loadSamples: (id: string, count: number) => Promise<void>;
}

const AccountsEmployerContext = createContext<AccountsEmployerContextModel | undefined>(undefined);

export const AccountsEmployerProvider = ({children}: {children: React.ReactNode}) => {
    const [payrollDetailsState, setPayrollDetailsState] = useState(
        getDefaultState<AccountsEmployerPayrollDetailsModel>,
    );
    const [samplesState, setSamplesState] = useState(getDefaultState<AccountsEmployerSamplesModel[]>);

    const loadPayrollDetails = async (id: string, searchParams: string) => {
        const url = `/api/secured/accounts/payroll/${id}?${searchParams}`;

        setPayrollDetailsState(getLoadingState(payrollDetailsState));
        const response = await apiRequest(url);
        setPayrollDetailsState(getLoadedState(response));
    };

    const loadSamples = async (id: string, count: number) => {
        const url = `/api/secured/accounts/samples/${id}?count=${count}`;

        setSamplesState(getLoadingState(samplesState));
        const response = await apiRequest(url);
        setSamplesState(getLoadedState(response));
    };

    const value: AccountsEmployerContextModel = {
        payrollDetailsState,
        loadPayrollDetails,
        samplesState,
        loadSamples,
    };

    return <AccountsEmployerContext.Provider value={value}>{children}</AccountsEmployerContext.Provider>;
};

export function useAccountsEmployer() {
    const context = useContext(AccountsEmployerContext);
    if (!context) {
        throw new Error('useAccountsEmployer must be used within AccountsEmployerProvider');
    }
    return context;
}
