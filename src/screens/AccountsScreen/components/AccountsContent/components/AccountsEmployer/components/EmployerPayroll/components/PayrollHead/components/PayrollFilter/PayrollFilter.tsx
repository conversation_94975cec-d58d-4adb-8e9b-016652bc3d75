import React, {useEffect} from 'react';
import {Button, Grid2, TextField} from '@mui/material';
import {useAccountsEmployer} from './../../../../../../useAccountsEmployer';
import {PayrollFilterValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import {KasDatePickerFormField} from '@/components';
import dayjs from 'dayjs';
import {PayrollGroupSelect} from '@/screens/AccountsScreen/components/AccountsContent/components';

export const PayrollFilter = ({employerId}: {employerId: string}) => {
    const {loadPayrollDetails, payrollDetailsState} = useAccountsEmployer();
    const disabled = !employerId || payrollDetailsState.loading;

    const onSubmit = async (values: PayrollFilterValues) => {
        const searchParams = new URLSearchParams({
            dateFrom: dayjs(values.dateFrom).format('YYYYMMDD'),
            months: values.months.toString(),
        }).toString();

        await loadPayrollDetails(values.payrollGroupId, searchParams);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            payrollGroupId: '',
            dateFrom: '',
            months: 6,
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        formik.resetForm();
    }, [employerId]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={2.5} />
                <Grid2 size={2.5}>
                    <PayrollGroupSelect
                        employerId={employerId}
                        name='payrollGroupId'
                        formik={formik}
                        disabled={disabled}
                    />
                </Grid2>
                <Grid2 size={2.5}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='dateFrom'
                        label='Start Date'
                        disabled={disabled}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <TextField
                        fullWidth
                        size='small'
                        name='months'
                        value={formik.values.months}
                        disabled={disabled}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Months'
                        variant='outlined'
                        type='number'
                        error={!!formik.errors.months && formik.touched.months}
                        helperText={formik.touched.months && formik.errors.months}
                    />
                </Grid2>
                <Grid2 size={2.5} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        loading={payrollDetailsState.loading}
                        disabled={!formik.isValid || disabled}>
                        Load
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
