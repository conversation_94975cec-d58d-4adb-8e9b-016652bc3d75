import React from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {AccountsDebitsColumns} from './tables';
import {TableView} from '@/views';
import {useAccountsDebits} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsDebitModel} from '@/interfaces';

export const AccountsDebitsResult = () => {
    const {debitsState} = useAccountsDebits();

    return (
        <TableView<AccountsDebitModel>
            withTableActions
            columns={AccountsDebitsColumns as ColumnDef<AccountsDebitModel, unknown>[]}
            loading={debitsState.loading}
            error={debitsState.error}
            data={debitsState.data}
            tableName='Accounts Debits'
            sortingColumns={[{id: 'amount', desc: true}]}
        />
    );
};
