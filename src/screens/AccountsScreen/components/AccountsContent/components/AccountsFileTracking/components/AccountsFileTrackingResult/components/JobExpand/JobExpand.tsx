import React, {Fragment, useEffect, useState} from 'react';
import {AccountsJobLogDetailModel, AccountsJobLogModel, OriginationLogRecordModel} from '@/interfaces';
import {KasLoading, Ka<PERSON>LoadingError, KasNoR<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Grid2, Typography, useTheme} from '@mui/material';

export const JobExpand = ({data}: {data: AccountsJobLogModel}) => {
    const {palette} = useTheme();
    const [state, setState] = useState(getDefaultState<AccountsJobLogDetailModel[]>());
    const loadDetails = async () => {
        const url = `/api/secured/accounts/file-tracking/job/${data.gid}`;

        setState(getLoadingState(state));
        const response = await apiRequest(url);

        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <KasLoadingError error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!state.data || !state.data.length}>
                <KasNoResults text='No records found' p={2} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                <Grid2 container spacing={1} pb={1} pl={6} sx={{color: palette.error.main}}>
                    <Grid2 size={2}>
                        <Typography variant='subtitle1'>Record</Typography>
                    </Grid2>
                    <Grid2 size={10}>
                        <Typography variant='subtitle1'>Message</Typography>
                    </Grid2>
                    {((state.data as OriginationLogRecordModel[]) || []).map((item) => (
                        <Fragment key={item.key}>
                            <Grid2 size={2}>{item.key}</Grid2>
                            <Grid2 size={10}>{item.message}</Grid2>
                        </Fragment>
                    ))}
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
