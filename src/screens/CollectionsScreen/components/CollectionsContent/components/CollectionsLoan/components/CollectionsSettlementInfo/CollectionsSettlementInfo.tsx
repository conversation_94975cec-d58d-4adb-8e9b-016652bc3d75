import React, {useMemo} from 'react';
import {Drawer, Stack, Typography} from '@mui/material';
import {KasInfo, KasInfoPreviewLoading, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {useCollectionsLoan} from './../../useCollectionsLoan';
import {toCurrency} from '@/utils/FormatUtils';
import {ExampleItem, SettlementCalculator} from './components';

export const CollectionsSettlementInfo = () => {
    const {loanState, loadLoanData, openSettlement, setOpenSettlement} = useCollectionsLoan();

    const balance = useMemo(() => {
        if (loanState.data) {
            return loanState.data.loan_balance;
        }

        return 0;
    }, [loanState.data]);

    return (
        <Drawer anchor='right' open={openSettlement} onClose={() => setOpenSettlement(false)}>
            <Stack spacing={1} p={2.5}>
                <Typography variant='h3'>Settlement Info</Typography>
                <div>
                    <Typography variant='subtitle1'>Formula</Typography>
                    <Typography variant='caption'>Recovery Ratio Amount</Typography>
                </div>
                <KasSwitch>
                    <KasSwitchWhen condition={loanState.loading}>
                        <KasInfoPreviewLoading />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!loanState.error}>
                        <KasLoadingError error={loanState.error} onTryAgain={loadLoanData} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!loanState.data}>
                        <KasInfo label='Example:' isInline>
                            {toCurrency(balance)}
                        </KasInfo>
                        <ExampleItem balance={balance} percent={0.85} />
                        <ExampleItem balance={balance} percent={0.75} />
                        <ExampleItem balance={balance} percent={0.5} />
                        <Typography variant='subtitle1' pt={2}>
                            Calculator
                        </Typography>
                        <SettlementCalculator loanBalance={balance} />
                    </KasSwitchWhen>
                </KasSwitch>
            </Stack>
        </Drawer>
    );
};
