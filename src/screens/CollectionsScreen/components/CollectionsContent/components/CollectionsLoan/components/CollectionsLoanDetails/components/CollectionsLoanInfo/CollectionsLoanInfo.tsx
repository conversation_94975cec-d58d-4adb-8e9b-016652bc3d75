import React, {useMemo} from 'react';
import {Grid2, Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {toCurrency} from '@/utils/FormatUtils';
import dayjs from 'dayjs';
import {ExternalLink, LoanCollectionAgency} from './components';
import {useCollectionsLoan} from '@/screens/CollectionsScreen/components/CollectionsContent/components';
import {RetainedLoanNotice} from '@/views/loan';

export const CollectionsLoanInfo = () => {
    const {loanState, employee, loanDetailsState, delinquencyState} = useCollectionsLoan();

    const letterLastSent = useMemo(() => {
        if (delinquencyState.data?.notifications) {
            const latestLetter = delinquencyState.data.notifications.filter(
                ({notification_type}) => notification_type === 'LETTER',
            );

            if (latestLetter.length) {
                const letterDate = latestLetter.map((d) => d.sent_time).reduce((a, b) => (a > b ? a : b));
                return letterDate.toString();
            }
        }

        return null;
    }, [delinquencyState.data?.notifications]);

    return (
        <Grid2 container spacing={2}>
            <Grid2 size={4}>
                <Stack>
                    <KasInfo label='Loan ID:' isInline>
                        {employee.current_loan_id}
                    </KasInfo>
                    <KasInfo label='LOS:' isInline>
                        {loanDetailsState.data?.origination_source || 'KAS'}
                    </KasInfo>
                    <KasInfo label='Payoff Amount:' isInline>
                        <abbr title={`util ${loanDetailsState.data?.payoff_until}`}>
                            {toCurrency(loanDetailsState.data?.payoff_amount)}
                        </abbr>
                    </KasInfo>
                    <KasInfo label='Term:' isInline>
                        {toCurrency(loanDetailsState.data?.installment_amount)} x{' '}
                        {loanDetailsState.data?.term} pmts
                    </KasInfo>
                    <KasInfo label='Maturity Date:' isInline>
                        {dayjs(loanDetailsState.data?.maturity_date).format('MM/DD/YYYY')}
                    </KasInfo>
                </Stack>
            </Grid2>
            <Grid2 size={4}>
                <Stack>
                    <KasInfo label='Dlq Days(current):' isInline>
                        {loanDetailsState.data?.rolling_dlq2_days_cur || 0}
                    </KasInfo>
                    <KasInfo label='Dlq Days(max):' isInline>
                        {loanDetailsState.data?.rolling_dlq2_days_max || 0}
                    </KasInfo>
                    <KasInfo label='Dlq Amount(current):' isInline>
                        {toCurrency(loanDetailsState.data?.rolling_dlq2_amount_cur || 0)}
                    </KasInfo>
                    <KasInfo label='Dlq Amount(max):' isInline>
                        {toCurrency(loanDetailsState.data?.rolling_dlq2_amount_max || 0)}
                    </KasInfo>
                    <KasInfo label='SOL End Date' isInline>
                        {loanDetailsState.data?.statute_limit_end_date ? loanDetailsState.data?.statute_limit_end_date + ' (' + loanDetailsState.data?.current_address_state + ')' : null}
                    </KasInfo>
                    <RetainedLoanNotice retained={loanDetailsState.data?.retained} />
                </Stack>
            </Grid2>
            <Grid2 size={4}>
                <Stack>
                    <KasInfo label='Collection Agency:' isInline>
                        {loanDetailsState.data?.loan_detailed_collections ? (
                            <LoanCollectionAgency
                                agencies={loanDetailsState.data.loan_detailed_collections}
                            />
                        ) : null}
                    </KasInfo>
                    <KasInfo label='Letter Last Sent:' isInline>
                        {letterLastSent}
                    </KasInfo>
                    <KasInfo label='Last Payment Date:' isInline>
                        {delinquencyState.data?.details?.last_payment_date
                            ? dayjs(delinquencyState.data.details.last_payment_date).format('MM/DD/YYYY')
                            : null}
                    </KasInfo>
                    <KasInfo label='Last Payment Amount:' isInline>
                        {toCurrency(delinquencyState.data?.details?.last_payment_amount || 0)}
                    </KasInfo>
                    <KasInfo label='Reminders:' isInline>
                        {loanState.data?.reminder_time ? (
                            <>
                                {loanState.data?.reminder_code} on{' '}
                                {dayjs(loanState.data?.reminder_time).format('MM/DD/YYYY')}
                            </>
                        ) : null}
                    </KasInfo>
                    <KasInfo label='External Link:' isInline>
                        <ExternalLink employeeId={employee.gid} />
                    </KasInfo>
                </Stack>
            </Grid2>
        </Grid2>
    );
};
