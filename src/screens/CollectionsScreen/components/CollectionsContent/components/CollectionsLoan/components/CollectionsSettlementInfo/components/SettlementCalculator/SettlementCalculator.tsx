import React, {useState} from 'react';
import {TextField, InputAdornment, Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {toPercentage} from '@/utils/FormatUtils';

export const SettlementCalculator = ({loanBalance}: {loanBalance: number}) => {
    const [value, setValue] = useState(loanBalance);

    return (
        <Stack spacing={2}>
            <TextField
                fullWidth
                size='small'
                value={value}
                onChange={(e) => setValue(Number(e.target.value))}
                label='Recovery Amount'
                variant='outlined'
                type='number'
                slotProps={{
                    input: {
                        startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                    },
                    htmlInput: {
                        step: '0.01',
                    },
                }}
            />
            <KasInfo label='Recovery Ratio Amount:' isInline>
                {toPercentage(value / loanBalance)}
            </KasInfo>
        </Stack>
    );
};
