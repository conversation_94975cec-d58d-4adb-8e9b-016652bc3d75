import {defaultInfoColumn} from '@/utils/TableUtils';
import {CellContext} from '@tanstack/react-table';
import { OnboardingConfigDTO } from '@/models/OnboardingConfigDTO';
import { OpenEmployerActionButton } from '../components/OpenEmployerActionButton/OpenEmployerActionButton';


const _defaultInfoColumn = defaultInfoColumn<OnboardingConfigDTO>;



export const OnboardingEmployerTable = [
    _defaultInfoColumn('mnemonic', 'Mnemonic'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<OnboardingConfigDTO, string>) => (
            <OpenEmployerActionButton data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
