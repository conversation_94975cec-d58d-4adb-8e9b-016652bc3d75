
import React, {ChangeEvent} from 'react';
import dayjs from 'dayjs';

import {KasDatePickerFormField} from '@/components';
import { useOnboardingEmployer } from '../../useOnboardingEmployer';
import { OnboardingConfigPayrollGroup, OnboardingConfigPayrollGroupFrequency, OnboardingEmployerDetailItemModel } from '@/models/OnboardingConfigDTO';
import {Button, MenuItem, Paper, Select, Switch, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField} from '@mui/material';
import { Field, FieldArray } from 'formik';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { v4 as uuidv4 } from 'uuid';



export const OnboardingEmployerDetailPayrollContents = ( ) => {
 
    const {formik, formEnabled} = useOnboardingEmployer();

    return (
        <FieldArray name="payroll_groups" validateOnChange={false}>
            {({ push, remove }) => (
                <div style={{padding: '15px'}}>
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>Frequency</TableCell>
                                    <TableCell>Mode</TableCell>
                                    <TableCell>Alias</TableCell>
                                    <TableCell>SampleDate</TableCell>
                                    <TableCell>DedLeadDays</TableCell>
                                    <TableCell>DedWarnDays</TableCell>
                                    <TableCell>DedLookback</TableCell>
                                    <TableCell>1stInstallLeadDays</TableCell>
                                    <TableCell>Active</TableCell>
                                    <TableCell>
                                        <Button
                                            variant="contained"
                                            onClick={() => push({
                                                active: true,
                                                key: uuidv4()
                                            } as OnboardingConfigPayrollGroup)}
                                            disabled={!formEnabled}
                                        >
                                            <AddIcon fontSize='small' />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {(formik.values.payroll_groups || []).map((_, index) => (
                                    <TableRow key={index}>
                                        <TableCell>
                                            <Field
                                                as={Select}
                                                name={`payroll_groups.${index}.payroll_frequency`}
                                                fullWidth
                                                displayEmpty
                                                disabled={!formEnabled}
                                                value={formik.values.payroll_groups[index].payroll_frequency || ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.payroll_frequency`, value === "" ? undefined : value);
                                                }}
                                            >
                                                <MenuItem key="" value=""><br /></MenuItem>
                                                {Object.values(OnboardingConfigPayrollGroupFrequency)
                                                    .filter(value => typeof value === "string")
                                                    .map((value) => (
                                                        <MenuItem key={value} value={value}>
                                                            {value}
                                                        </MenuItem>
                                                ))}
                                            </Field>
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.mode`}
                                                fullWidth
                                                disabled={!formEnabled}
                                                value={_.mode ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.mode`, value === "" ? undefined : value);
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.alias`}
                                                fullWidth
                                                disabled={!formEnabled}
                                                value={_.alias ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.alias`, value === "" ? undefined : value);
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <KasDatePickerFormField 
                                                name={`payroll_groups.${index}.sample_date`} 
                                                formik={formik} 
                                                minDate={dayjs().subtract(180, 'day')}
                                                disabled={!formEnabled}
                                                label=''
                                                dataFormat='YYYY-MM-DD'/>
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.deduction_lead_days`}
                                                fullWidth
                                                type='number'
                                                disabled={!formEnabled}
                                                value={_.deduction_lead_days ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.deduction_lead_days`, value === "" ? undefined : Number(value));
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.deduction_warning_days`}
                                                fullWidth
                                                type='number'
                                                disabled={!formEnabled}
                                                value={_.deduction_warning_days ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.deduction_warning_days`, value === "" ? undefined : Number(value));
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.deduction_lookback_days`}
                                                fullWidth
                                                type='number'
                                                disabled={!formEnabled}
                                                value={_.deduction_lookback_days ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.deduction_lookback_days`, value === "" ? undefined : Number(value));
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Field
                                                as={TextField}
                                                name={`payroll_groups.${index}.first_installment_lead_days`}
                                                fullWidth
                                                type='number'
                                                disabled={!formEnabled}
                                                value={_.first_installment_lead_days ?? ""}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                                    const value = e.target.value.trim();
                                                    formik.setFieldValue(`payroll_groups.${index}.first_installment_lead_days`, value === "" ? undefined : Number(value));
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                        <Field
                                            name={`payroll_groups.${index}.active`}
                                            type="checkbox">
                                                {({ field }: { field: any }) => (
                                                    <Switch
                                                        {...field}
                                                        checked={Boolean(field.value)}
                                                        onChange={(e) => formik.setFieldValue(field.name, e.target.checked)}
                                                        disabled={!formEnabled}
                                                    />
                                                )}
                                        </Field>
                                        </TableCell>
                                        <TableCell>
                                            <Button
                                                variant="outlined"
                                                color="error"
                                                onClick={() => remove(index)}
                                                disabled={!formEnabled}
                                            >
                                                <DeleteIcon fontSize='small'  />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </div>
            )}
        </FieldArray>
    );
};

