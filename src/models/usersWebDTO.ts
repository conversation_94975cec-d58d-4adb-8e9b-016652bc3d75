import {LoanStatusType} from '@/models/index';

/**
 * Kashable API
 * Rest API for Kashable Portal
 *
 * OpenAPI spec version: 20210810
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
export type KasDesignation = 'SUPER_USER' | 'MANAGER' | 'TEAM_LEAD' | 'POWER' | 'READ';

export type ExtendedRoleEnum = UsersWebDTO.RoleEnum | `${UsersWebDTO.RoleEnum}:${KasDesignation}`;

export interface UsersWebDTO {
    address_state: string;
    create_time?: Date;
    email?: string;
    email_verified: boolean;
    employer?: string;
    employment_verified: boolean;
    first_name?: string;
    gender?: string;
    gid?: number;
    last_name?: string;
    last_update_time?: Date;
    leadgen?: string;
    loan_id?: number;
    loan_status?: LoanStatusType;
    locked?: boolean;
    manual_review_date?: Date;
    phone?: string;
    phone_verified: boolean;
    referrer_code?: string;
    roles: ExtendedRoleEnum[];
    workflow?: string;
    has_avatar: boolean;
    has_pay_rate: boolean;
    refinance_eligible: boolean;
    last_login_ip?: string;
}

export namespace UsersWebDTO {
    export type RoleEnum =
        | 'KASH_POWERUSER'
        | 'KASH_ADMIN'
        | 'KASH_COMPLIANCE'
        | 'KASH_HR'
        | 'KASH_SUPPORT'
        | 'KASH_OPERATOR'
        | 'KASH_ACCOUNTING'
        | 'KASH_MONITOR'
        | 'KASH_UNDERWRITING'
        | 'KASH_VERIFICATION'
        | 'KASH_COLLECTIONS'
        | 'KASH_SALES'
        | 'KASH_ACCOUNTS'
        | 'KASH_SYSTEM'
        | 'KASH_ONBOARDING'
        | 'KASH_ONBOARDING_QA';

    export const RoleEnum = {
        KASH_POWERUSER: 'KASH_POWERUSER' as RoleEnum,
        KASH_ADMIN: 'KASH_ADMIN' as RoleEnum,
        KASH_COMPLIANCE: 'KASH_COMPLIANCE' as RoleEnum,
        KASH_HR: 'KASH_HR' as RoleEnum,
        KASH_SUPPORT: 'KASH_SUPPORT' as RoleEnum,
        KASH_OPERATOR: 'KASH_OPERATOR' as RoleEnum,
        KASH_ACCOUNTING: 'KASH_ACCOUNTING' as RoleEnum,
        KASH_MONITOR: 'KASH_MONITOR' as RoleEnum,
        KASH_UNDERWRITING: 'KASH_UNDERWRITING' as RoleEnum,
        KASH_VERIFICATION: 'KASH_VERIFICATION' as RoleEnum,
        KASH_COLLECTIONS: 'KASH_COLLECTIONS' as RoleEnum,
        KASH_SALES: 'KASH_SALES' as RoleEnum,
        KASH_ACCOUNTS: 'KASH_ACCOUNTS' as RoleEnum,
        KASH_SYSTEM: 'KASH_SYSTEM' as RoleEnum,
        KASH_ONBOARDING: 'KASH_ONBOARDING' as RoleEnum,
        KASH_ONBOARDING_QA: 'KASH_ONBOARDING_QA' as RoleEnum,
    };
}
