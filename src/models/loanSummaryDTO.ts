/**
 * Kashable API
 * Rest API for Kashable Portal
 *
 * OpenAPI spec version: 20210810
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import {Payment} from './payment';
import {LoanStatusType} from '@/models/index';

export interface LoanSummaryDTO {
    additional_income?: string;
    amount?: string;
    application_id?: number;
    apr?: string;
    balance?: string;
    close_date?: Date;
    create_time?: Date;
    disbursement_date?: Date;
    delinquent_amount?: string;
    delinquent_days?: number;
    deposit_eligible?: boolean;
    effective_interest_rate?: number;
    employee_id?: number;
    expected_payoff_date?: Date;
    first_installment_date?: Date;
    gid?: number;
    installment_amount?: string;
    installment_count?: number;
    installment_total?: string;
    installments_remaining?: number;
    interest?: string;
    interest_amount?: string;
    interest_rate?: string;
    last_update_time?: Date;
    leadgen?: {[key: string]: string};
    loan_status?: LoanStatusType;
    max_loan_available?: number;
    next_installment_amount?: string;
    next_installment_date?: Date;
    origination_fee?: string;
    origination_source?: LoanSummaryDTO.OriginationSourceEnum;
    payments?: Array<Payment>;
    payoff_amount?: string;
    payoff_progress?: number;
    payoff_until?: Date;
    payroll_deduction_status?: string;
    processing_fee?: string;
    revenue?: string;
    start_date?: Date;
    term?: number;
    total_amount?: string;
    total_paid?: string;
}
export namespace LoanSummaryDTO {
    export type OriginationSourceEnum = 'KAS' | 'CRB' | 'BRB' | 'MDB';
    export const OriginationSourceEnum = {
        KAS: 'KAS' as OriginationSourceEnum,
        CRB: 'CRB' as OriginationSourceEnum,
        BRB: 'BRB' as OriginationSourceEnum,
        MDB: 'MDB' as OriginationSourceEnum,
    };
}
