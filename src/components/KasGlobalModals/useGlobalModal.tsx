import React, {createContext, useContext, useState} from 'react';
import {KasGlobalModals} from '@/components';
import {GlobalModalProps} from './interfaces';

interface GlobalModalContextProps {
    showGlobalModal: (modal: GlobalModalProps) => void;
    hideGlobalModal: (actionPerformed?: boolean) => void;
    modal: GlobalModalProps | null;
    setGlobalModalCallback: (callback: (modal: GlobalModalProps) => void) => void;
}

const GlobalModalContext = createContext<GlobalModalContextProps | undefined>(undefined);

export const GlobalModalProvider = ({children}: {children: React.ReactNode}) => {
    const [modal, setModal] = useState<GlobalModalProps | null>(null);
    const [modalCallback, setModalCallbackState] = useState<(modal: GlobalModalProps) => void>(() => {});

    const showGlobalModal = (modal: GlobalModalProps) => {
        setModal(modal);
    };

    const hideGlobalModal = (actionPerformed: boolean = false) => {
        setModal(null);

        if (actionPerformed && !!modal) {
            modalCallback(modal);
        }
    };

    const setGlobalModalCallback = (callback: (modal: GlobalModalProps) => void) => {
        setModalCallbackState(() => callback);
    };

    const value: GlobalModalContextProps = {
        showGlobalModal,
        hideGlobalModal,
        modal,
        setGlobalModalCallback,
    };

    return (
        <GlobalModalContext.Provider value={value}>
            {children}
            <KasGlobalModals />
        </GlobalModalContext.Provider>
    );
};

export const useGlobalModal = (): GlobalModalContextProps => {
    const context = useContext(GlobalModalContext);
    if (!context) {
        throw new Error('useGlobalModal must be used within a GlobalModalProvider');
    }
    return context;
};
