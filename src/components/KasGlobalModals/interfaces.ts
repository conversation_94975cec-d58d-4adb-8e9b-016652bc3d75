import {KasCommentFormValues} from '@/components/KasGlobalModals/components/KasCommentForm/schema';
import {AddressDTO} from '@/models';
import {LoanDetailsModel} from '@/interfaces/loan.interface';
import {AMLReportTableViewProps} from '@/views';

export enum GlobalModal {
    Unlink_User = 'Unlink_User',
    Remove_User = 'Remove_User',
    Unlock_User = 'Unlock_User',
    Employee_Address = 'Employee_Address',
    Employee_Phone = 'Employee_Phone',
    Employee_Email = 'Employee_Email',
    Merge_Employees = 'Merge_Employees',
    Purge_Employees = 'Purge_Employees',
    Confirm = 'Confirm',
    Comment = 'Comment',
    Debit_Payment = 'Debit_Payment',
    Verbal_ACH = 'Verbal_ACH',
    Communication_ACH = 'Communication_ACH',
    AML_Report = 'AML_Report',
    Refund_Email = 'Refund_Email',
}

export interface GlobalModalActionProps {
    userId: number;
    uid: string;
}

export interface GlobalModalEmployeeBaseProps {
    employeeId: number;
    method: 'put' | 'post';
    gid?: number;
}

export interface GlobalModalEmployeePhoneProps extends GlobalModalEmployeeBaseProps {
    phone?: string;
    source?: string;
}

export interface GlobalModalEmployeeAddressProps extends GlobalModalEmployeeBaseProps {
    address?: AddressDTO;
    source?: AddressDTO.SourceEnum;
}

export interface GlobalModalEmployeeEmailProps extends GlobalModalEmployeeBaseProps {
    email?: string;
    source?: string;
}

export interface GlobalModalMergeEmployeesProps {
    employeeId: number;
    approvable?: boolean;
    merge_to_employee_id?: number;
    request_id?: number;
    comments?: string[];
    onSuccess?: () => Promise<void>;
}

export interface GlobalModalPurgeEmployeesProps {
    employeeId: number;
}

export interface GlobalModalConfirmProps {
    title: string;
    onSubmit: () => Promise<boolean>;
    onClose: () => void;
}

export interface GlobalModalCommentProps {
    title: string;
    onSubmit: (values: KasCommentFormValues) => void;
    onClose?: () => void;
}

export interface GlobalModalDebitPaymentProps {
    loanId: number;
    payoffAmount?: number;
}

export interface GlobalModalVerbalACHProps {
    loan: LoanDetailsModel;
}

export interface GlobalModalCommunicationACHProps {
    loan: LoanDetailsModel;
}

export interface GlobalModalRefundEmailProps {
    bankId: number;
    onSuccess?: () => void;
}

export type GlobalModalProps =
    | {type: GlobalModal.Unlink_User; props: GlobalModalActionProps}
    | {type: GlobalModal.Remove_User; props: GlobalModalActionProps}
    | {type: GlobalModal.Unlock_User; props: GlobalModalActionProps}
    | {type: GlobalModal.Employee_Address; props: GlobalModalEmployeeAddressProps}
    | {type: GlobalModal.Employee_Phone; props: GlobalModalEmployeePhoneProps}
    | {type: GlobalModal.Employee_Email; props: GlobalModalEmployeeEmailProps}
    | {type: GlobalModal.Merge_Employees; props: GlobalModalMergeEmployeesProps}
    | {type: GlobalModal.Purge_Employees; props: GlobalModalPurgeEmployeesProps}
    | {type: GlobalModal.Confirm; props: GlobalModalConfirmProps}
    | {type: GlobalModal.Comment; props: GlobalModalCommentProps}
    | {type: GlobalModal.Debit_Payment; props: GlobalModalDebitPaymentProps}
    | {type: GlobalModal.Verbal_ACH; props: GlobalModalVerbalACHProps}
    | {type: GlobalModal.Communication_ACH; props: GlobalModalCommunicationACHProps}
    | {type: GlobalModal.AML_Report; props: AMLReportTableViewProps}
    | {type: GlobalModal.Refund_Email; props: GlobalModalRefundEmailProps};
