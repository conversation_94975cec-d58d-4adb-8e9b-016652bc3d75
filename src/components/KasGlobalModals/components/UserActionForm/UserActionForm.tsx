import './styles.scss';

import React, {useMemo, useState} from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {GlobalModalActionProps, KasModalFooter, useGlobalModal} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {removeProfile, updateSearch} from '@/lib/slices/underwritingSlice';
import {useAppDispatch} from '@/lib/hooks';
import {DEFAULT_ERROR_MSG} from '@/constants';

interface UserActionProps extends GlobalModalActionProps {
    action: 'unlink' | 'remove' | 'unlock';
}

const UserActionForm = ({userId, uid, action}: UserActionProps) => {
    const {hideGlobalModal} = useGlobalModal();
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const dispatch = useAppDispatch();

    const alertMessage = useMemo(() => {
        switch (action) {
            case 'unlink':
                return 'Are you sure you want to UNLINK this User?';
            case 'remove':
                return 'Are you sure you want to REMOVE this User?';
            case 'unlock':
                return 'Are you sure you want to UNLOCK this User?';
            default:
                return '';
        }
    }, [action]);

    const successMessage = useMemo(() => {
        switch (action) {
            case 'unlink':
                return 'Successfully Unlinked User';
            case 'remove':
                return 'Successfully Removed User';
            case 'unlock':
                return 'Successfully Unlocked User';
            default:
                return '';
        }
    }, [action]);

    const onSubmit = async () => {
        setSubmitting(true);
        const url = `/api/secured/power/user/${userId}/${action}`;
        const response = await apiRequest(url, {method: action === 'unlock' ? 'PUT' : 'DELETE'});

        if (response.value) {
            if (action === 'unlink' || action === 'remove') {
                dispatch(removeProfile(uid));
            }

            dispatch(updateSearch());
            showMessage(successMessage, 'success');
            hideGlobalModal(true);
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
            hideGlobalModal();
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form className='kas-user-action-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>{alertMessage}</Alert>
                </Grid2>
            </Grid2>
            <KasModalFooter disabled={submitting} submitText='OK' onCancel={hideGlobalModal} />
        </form>
    );
};

export const UnlinkUser = ({...props}: GlobalModalActionProps) => (
    <UserActionForm {...props} action='unlink' />
);

export const RemoveUser = ({...props}: GlobalModalActionProps) => (
    <UserActionForm {...props} action='remove' />
);

export const UnlockUser = ({...props}: GlobalModalActionProps) => (
    <UserActionForm {...props} action='unlock' />
);
