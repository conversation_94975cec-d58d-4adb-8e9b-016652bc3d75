import React, {useEffect, useRef} from 'react';
import {Autocomplete, AutocompleteProps, TextField} from '@mui/material';
import {FormikValues} from 'formik';
import {SelectModel} from '@/interfaces';

interface KasAutocompleteFieldProps<T>
    extends Partial<AutocompleteProps<SelectModel<T>, false, false, false>> {
    name: string;
    formik: FormikValues;
    options: SelectModel<T>[];
    label?: string;
    showValidation?: boolean;
}

export const KasAutocompleteField = <T,>({
    name,
    formik,
    options,
    label = 'Select',
    showValidation = true,
    ...rest
}: KasAutocompleteFieldProps<T>) => {
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (inputRef.current && formik.values[name]) {
            inputRef.current.focus();
        }
    }, [formik.values[name]]);

    return (
        <Autocomplete
            {...rest}
            size='small'
            fullWidth
            options={options}
            getOptionLabel={(option) => option.label}
            getOptionKey={(option) => option.id}
            value={options.find((option) => option.value === formik.values[name]) || null}
            isOptionEqualToValue={(option, value) => option.value === value.value}
            onChange={(_, newValue) => {
                formik.setFieldValue(name, newValue?.value || '');
            }}
            onBlur={() => {
                formik.setFieldTouched(name);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    inputRef={inputRef}
                    variant='outlined'
                    label={label}
                    error={showValidation && !!formik.errors[name] && formik.touched[name]}
                    helperText={showValidation && formik.touched[name] && formik.errors[name]}
                />
            )}
        />
    );
};
