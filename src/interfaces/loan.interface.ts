import {LoanStatusType} from '@/models';

export interface LoanDetailedCollection {
    collection_agency_name: string;
    collection_agency_date: string;
    collection_agency_close_date: string | null;
}

export interface LoanDetailsModel {
    gid: number;
    employee_id: number;
    amount: string;
    balance: string;
    running_balance: string;
    max_loan_available: string | null;
    disbursement_amount: string | null;
    installment_amount: string;
    installment_count: number;
    bankruptcy: boolean | null;
    term: number;
    apr: string;
    interest: string;
    interest_rate: string;
    effective_interest_rate: string | null;
    origination_fee: string;
    processing_fee: string;
    penalty_fees: string | null;
    start_date: string;
    funding_date: string;
    disbursement_date: string;
    first_installment_date: string;
    next_installment_date: string | null;
    close_date: string | null;
    loan_status: LoanStatusType;
    payroll_deduction_status: string;
    arbitration_agreement_status: string | null;
    loan_prepaid: boolean;
    prepayment_refund_owed: string | null;
    prepayment_refund_paid: string | null;
    collection_agency_id: number | null;
    loan_agreement_id: number | null;
    payroll_authorization_agreement_id: number | null;
    arbitration_agreement_id: number | null;
    document_by_signed_payroll_authorization_id: number | null;
    document_by_signed_arbitration_id: number | null;
    document_by_signed_loan_id: number | null;
    create_time: string | null;
    last_update_time: string | null;
    address_state: string;
    dlq_date: string;
    fcra_first_dlq_date: string;
    dlq_days: number;
    dlq_amount: string;
    dlq2_amount: null;
    rolling_dlq_date: string;
    rolling_dlq2_date: string;
    rolling_dlq_days_cur: number;
    rolling_dlq2_days_cur: number;
    rolling_dlq_days_max: number;
    rolling_dlq2_days_max: number;
    rolling_dlq_amount_cur: string;
    rolling_dlq2_amount_cur: string;
    rolling_dlq_amount_max: string;
    rolling_dlq2_amount_max: string;
    principal_balance: number;
    accrued_interest: number;
    accrued_fee: number;
    running_balance2: string;
    last_payment_date: string;
    loan_detailed_collections: LoanDetailedCollection[];
    loan_purpose: string;
    payoff_amount: string;
    payoff_until: string;
    paid_amount: string;
    revenue_balance: string;
    application_date: string;
    signature_date: string;
    maturity_date: string;
    maturity_payoff_amount: string;
    paid: boolean;
    delinquent: boolean;
    open: boolean;
    retained: boolean;
    context: string | null;
    origination_id: number | null;
    origination_source: string | null;
    origination_status: string | null;
    purchase_date: string | null;
    origination_last_update_time: string | null;
    origination_external_status_time: string | null;
    debt_validation_sent_date: string | null;
    referrer_entity_id: number | null;
    referrer_entity_class: string | null;
    referrer_name: string | null;
    refund_amount: number;
    litigation: boolean | null;
    litigation_start_date: string | null;
    litigation_end_date: string | null;
    backup_ach_pause_date: string | null;
    backup_ach_active: boolean | null;
    backup_ach_payment_status: string | null;
    statute_limit_end_date: string | null;
    current_address_state: string | null;
}
export interface CreditReportingValueModel {
    code: string;
    value: string | null;
    month?: string;
}

export interface LoanCreditReportingModel {
    report_date: number;
    payment_status: CreditReportingValueModel;
    payment_rating: CreditReportingValueModel;
    consumer_information_indicator: CreditReportingValueModel;
    rating_history: CreditReportingValueModel[];
    job_id: number;
}

export interface LoanCreditReportDetailsModel {
    first_name: string;
    last_name: string;
    social_security_number: string;
    date_of_birth: string;
    original_loan_amount: string;
    scheduled_monthly_payment_amount: string;
    actual_payment_amount: string;
    current_balance: string;
    original_chargeoff_amount: string;
    terms_duration: number;
    terms_frequency: string;
    account_status: string;
    special_comment: string;
    date_of_first_delinquency: string;
    date_closed: string;
    date_of_last_payment: string;
    interest_type_indicator: string;
    consumer_information_indicator: string;
}

export interface LoanDelinquencyModel {
    date: string;
    last_current: string;
    dlq_days_cur: number;
    dlq_days_max: number;
    dlq_amount_cur: number;
    dlq_amount_max: number;
}

export interface LoanAlternativePaymentModel {
    gid: number;
    loan_id: number;
    vendor_name: string;
    payment_type: string;
    payment_code: string;
    payment_mode: string;
    payment_amount: string;
    payoff_amount: string;
    payoff_until: string;
    receipt_id: number;
    cleared_id: number;
    cleared_date: string;
    active: boolean;
    request_date: string;
    process_date: string;
    create_time: string;
    last_update_time: string;
    schedule: boolean;
}

export interface LoanRepaymentScheduleModel {
    gid: number;
    loan_id: number;
    date: string;
    principal: string;
    interest: string;
    fee: string;
    total: string;
    deduction: boolean;
    modified: boolean;
}

export interface LoanAlternativeScheduleModel {
    gid: number;
    alternative_payment_id: number;
    date: string;
    amount: string;
    process_date: string | null;
    cleared_id: number | null;
    cleared_date: string | null;
    active: string | null;
    create_time: string;
}
