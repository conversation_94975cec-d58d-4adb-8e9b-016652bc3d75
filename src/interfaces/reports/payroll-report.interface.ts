export interface PayrollReportModel {
    gid: number;
    employee_id: number;
    provider: string;
    records: number;
    report_date: string;
}

export interface PayrollReportElectionModel {
    bank_name: string | null;
    priority: number | null;
    amount: string | null;
    percentage: string | null;
    routing_number: string | null;
    account_name: string | null;
    account_number: string | null;
}

export interface PayrollReportDetailsModel {
    report_id: string | null;
    created_time: string | null;
    employer: string;
    elections: PayrollReportElectionModel[];
}
