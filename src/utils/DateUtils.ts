import dayjs, {Dayjs} from 'dayjs';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import {DayJsRangeModel} from '@/interfaces';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import utc from 'dayjs/plugin/utc';
import tz from 'dayjs/plugin/timezone';
import {DEFAULT_DATE_FORMAT} from '@/constants';

dayjs.extend(customParseFormat);
dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(tz);

interface ParsedDateModel {
    year?: string;
    month?: string;
    day?: string;
}

const isWorkingDay = (date: Dayjs): boolean => {
    const dayOfWeek = date.day();
    return dayOfWeek >= 1 && dayOfWeek <= 5;
};

export const getPreviousWorkingDay = (daysToSubtract: number): Dayjs => {
    let previousDay = dayjs();

    for (let i = 0; i < daysToSubtract; i++) {
        previousDay = previousDay.subtract(1, 'day');

        while (!isWorkingDay(previousDay)) {
            previousDay = previousDay.subtract(1, 'day');
        }
    }

    return previousDay;
};

export const getEndOfMonth = (date: Dayjs): Dayjs => {
    let eom = date.add(1, 'month');
    eom = eom.set('date', 1);
    eom = eom.add(-1, 'day');

    return eom;
};

export const getBeginningOfPreviousFullMonth = () => {
    const endOfPrev = getEndOfPreviousFullMonth();
    return endOfPrev.set('date', 1);
};

export const getEndOfPreviousFullMonth = () => {
    const today = dayjs();
    const eom = getEndOfMonth(today);
    if (eom.isSame(today)) {
        return today;
    } else {
        const firstOfMonth = today.set('date', 1);
        return firstOfMonth.add(-1, 'day');
    }
};

export const parseDate = (date: string): ParsedDateModel => {
    const curDateArr = date.split('-');

    return {year: curDateArr[0], month: curDateArr[1], day: curDateArr[2]};
};

export const getLastDayOfMonth = (date: Dayjs | null) => {
    return dayjs(date).endOf('month');
};

export const getHumanizedDuration = (date: string) => {
    const eligibleDate = dayjs(date);
    const now = dayjs();
    const daysDiff = eligibleDate.diff(now, 'days');

    return dayjs.duration({days: daysDiff}).humanize(true);
};

export const getHumanizedTimeDuration = (ms: number) => {
    return dayjs.duration(ms).humanize(true);
};

export const isValidRangeDate = (value: DayJsRangeModel) => {
    return !!(value.start && value.end);
};

export const getTimezoneTime = (timezone?: string): Dayjs => {
    return dayjs().tz(timezone || 'America/New_York');
};

export const getCustomParseFormatDate = (date: string, format: string) => dayjs(date, format);

export const formatTimeStampISO = (date: Date) => formatDate(date, 'YYYY-MM-DD HH:mm:ss');

export const getDaysMatrix = (month: Dayjs): (Dayjs | null)[][] => {
    const start = month.startOf('month').startOf('week');
    let date = start.clone();
    const weeks: (Dayjs | null)[][] = [];

    for (let w = 0; w < 6; w++) {
        const week: (Dayjs | null)[] = [];
        for (let i = 0; i < 7; i++) {
            week.push(date.clone());
            date = date.add(1, 'day');
        }
        weeks.push(week);
    }

    return weeks;
};

export const getNextBusinessDay = (date?: string | Date): string => {
    let nextDay = dayjs(date).add(1, 'day');

    while (nextDay.day() === 0 || nextDay.day() === 6) {
        nextDay = nextDay.add(1, 'day');
    }

    return formatDate(nextDay, 'YYYY-MM-DD');
};

export const formatDate = (date?: string | number | Date | Dayjs | null, format = DEFAULT_DATE_FORMAT) => {
    return date ? dayjs(date).format(format) : '';
};
