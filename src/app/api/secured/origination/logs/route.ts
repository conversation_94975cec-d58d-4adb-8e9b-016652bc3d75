import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationLogModel} from '@/interfaces';
import {AxiosError} from 'axios';

const getLogs = (length: string | null) => {
    const url = `/secured/origination/funding/status?${length ? `length=${length}` : ''}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationLogModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest) {
    const length = request.nextUrl.searchParams.get('length');
    const response = await getLogs(length);

    return NextResponse.json(response, {status: response.code});
}
