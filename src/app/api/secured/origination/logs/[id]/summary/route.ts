import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationLogSummaryModel} from '@/interfaces';
import {AxiosError} from 'axios';

const getLogSummary = (id: string) => {
    const url = `/secured/origination/funding/summary/${id}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationLogSummaryModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest, {params}: {params: {id: string}}) {
    const {id} = params;
    const response = await getLogSummary(id);

    return NextResponse.json(response, {status: response.code});
}
