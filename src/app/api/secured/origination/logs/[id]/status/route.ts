import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationLogRecordModel} from '@/interfaces';
import {AxiosError} from 'axios';

const getLogRecords = (id: string) => {
    const url = `/secured/origination/funding/status/${id}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationLogRecordModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest, {params}: {params: {id: string}}) {
    const {id} = params;
    const response = await getLogRecords(id);

    return NextResponse.json(response, {status: response.code});
}
