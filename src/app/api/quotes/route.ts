import {NextResponse} from 'next/server';
import axios, {AxiosError} from 'axios';

import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import QuoteDTO from '@/models/quoteDTO';
import {Completable} from '@/interfaces';

const API_KEY = process.env.NEXT_PUBLIC_PROXY_QUOTES_API_KEY || '';
const BASE_URL = process.env.NEXT_PUBLIC_QUOTES_URI || '';

const quotesBase = axios.create({
    headers: {
        'X-Api-Key': API_KEY,
    },
    baseURL: BASE_URL,
});

const getQuote: (category: string) => Promise<Completable<QuoteDTO[]>> = (category) => {
    const params = {category};

    return quotesBase
        .get<QuoteDTO[]>('/quotes', {params})
        .then((res) => mapSucceeded(res, (d) => d))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(_request: Request) {
    const response = await getQuote('happiness');

    return NextResponse.json(response, {status: response.code});
}
